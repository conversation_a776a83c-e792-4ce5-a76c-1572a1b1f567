"use client";

import React from "react";
import getConfig from "next/config";
import styles from "./Banner.module.scss";
import { imageBaseUrl } from "@constants/envVariables";
import AnimatedTitleWithSubtitle from "components/common/animatedTitleWithSubtitle/AnimatedTitleWithSubtitle";
import { useTranslation } from "next-i18next";
import RotatingGlobe from "components/common/rotatingGlobe";

const Banner = () => {
  const { t, i18n } = useTranslation();

  const backgroundImage = `${imageBaseUrl}/images/solutionHubBg.png`;
  const globe = `${imageBaseUrl}/images/globe.png`;
  const locale = i18n.language;

  const subTitles = [t("bannerSubTitle1"), t("bannerSubTitle2")];

  return (
    <>
      <style jsx>{`
        .banner {
          background: url("${backgroundImage}");
          background-repeat: no-repeat;
          background-size: cover;
        }
      `}</style>
      <div className={`banner container ${styles["banner"]}`}>
      <RotatingGlobe />
        <div className={`blobe ${styles["banner__container"]}`}>

          <AnimatedTitleWithSubtitle
            title={t("solutionHub")}
            subtitle={subTitles}
            locale={locale}
          />
        </div>
      </div>
    </>
  );
};

export default Banner;
