"use client";

import React from "react";
import styles from "./CardsComponent.module.scss";
import Cards from "./cards/Cards";
import cardsData from "../cardsComponent/cardsData.json";
import { useTranslation } from "next-i18next";

/**
 * CardsComponent - A component that displays sections for both buyers and brands
 * @returns {JSX.Element} The rendered component
 */
const CardsComponent = () => {
  const { t } = useTranslation();
  return (
    <div className={`container all-card-container ${styles["cards-container"]}`}>
      {[...cardsData.buyers, ...cardsData.brands].map(
        (data: {
          label: string;
          title: string;
          description: string;
          type: string;
          cards: any[];
        }) => (
          <div
            key={data.title}
            className={`${styles["cards-container__card-contents"]} ${
              data.type === "buyers" && styles["brand-content"]
            }`}
          >
            <div className={styles["cards-container__chip"]}>
              <span></span>
              <p>{t(data.label)}</p>
            </div>
            <h3 className={styles["cards-container__heading"]}>
              {t(data.title)}
            </h3>
            <p className={styles["cards-container__description"]}>
              {t(data.description)}
            </p>
            <Cards cardsData={data.cards} type={data.type} page="home" />
          </div>
        )
      )}
    </div>
  );
};

export default CardsComponent;
