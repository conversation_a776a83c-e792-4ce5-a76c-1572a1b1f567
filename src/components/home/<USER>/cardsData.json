{"buyers": [{"label": "buyersLabel", "title": "<PERSON><PERSON><PERSON><PERSON>", "description": "buyersDescription", "type": "buyers", "cards": [{"id": 1, "title": "atWork", "slug": "atwork", "description": "workDesc", "meta": {"title": "@Work by YOUGotaGift | Bulk eGift Cards for Business", "description": "Buy eGift Cards in bulk with @Work. Customize, print, or send via SMS, WhatsApp, or email—perfect for employee rewards & corporate gifting."}, "tags": ["business", "bulkOrder", "customDesign", "assistedSales", "workPro"], "borderColor": "#99C6FF", "backgroundColor": "#2B343F", "tagsBg": "#2B282D", "index": 4}, {"id": 2, "title": "giftingWallet", "slug": "gifting-and-wallet", "description": "giftingDesc", "meta": {"title": "Gifting & Wallet | Buy, Personalize & Manage eGift Cards", "description": "Send personalized eGift Cards for any occasion. Shop top brands, group gift with friends & store all your cards in one smart, secure wallet."}, "tags": ["giftCatalog", "individualGifting", "groupGifting", "personalisation", "wallet"], "tagsBg": "#2B2A2C", "borderColor": "#FFD7EF", "backgroundColor": "#3F373C", "index": 3}, {"id": 3, "title": "cardTitle3", "slug": "gift-card-api", "description": "cardDesc3", "meta": {"title": "Gift Card API for Businesses | Fast, Secure & Scalable", "description": "Integrate Gift Cards with YOUGotaGift’s enterprise-grade API. Fast, secure, and scalable—trusted by 2,500+ businesses with 99.999% uptime."}, "tags": ["technology", "features", "brandNetwork", "experience", "ourAPIClient"], "tagsBg": "#0B0219", "borderColor": "#C2B8FF", "backgroundColor": "#13072D", "index": 2}, {"id": 4, "title": "giftShop", "slug": "giftshop", "description": "cardDesc4", "meta": {"title": "Giftshop API for Loyalty & Rewards | YOUGotaGift", "description": "Integrate a secure Gift Card store into your platform. Enable loyalty point redemption with fast, scalable APIs, built for digital rewards & gifting."}, "tags": ["storeFront", "technologyEdge", "loyaltyPoint", "shopFeatures"], "borderColor": "#D2C9FF", "backgroundColor": "#36343F", "tagsBg": "#2B2934", "index": 1}]}], "brands": [{"label": "brandsLabel", "title": "forBrands", "description": "brandsDescription", "type": "brands", "cards": [{"id": 1, "title": "cardProducts", "slug": "card-products", "description": "cardProdDesc", "meta": {"title": "Custom Gift Card Solutions for Brands | YOUGotaGift", "description": "Launch custom digital & physical gift cards for your brand. Offer closed or open loop cards, promo cards, or wallets—built for retail & promotions."}, "tags": ["giftCard", "singleBrands", "closedLoops", "openLoop"], "borderColor": "#524C72", "backgroundColor": "#13072D", "index": 6, "color": "#C2B8FF", "tagsBg": "#0B0219"}, {"id": 2, "title": "programManagement", "slug": "program-management", "description": "programDesc", "meta": {"title": "Gift Card Program Management | Full Lifecycle Control", "description": "Manage your gift card program with ease—branding, portals, reporting & fraud prevention, all in one secure and scalable solution."}, "tags": ["programSetup", "centralizedControl", "reportingSecurity", "corporatePortals", "customerSuport"], "borderColor": "#524C72", "backgroundColor": "#13072D", "index": 5, "tagsBg": "#0B0219", "color": "#C2B8FF"}, {"id": 3, "title": "campaignManagement", "slug": "campaign-management", "description": "campaignDesc", "meta": {"title": "Campaign Management | Targeted Gift Card Promotions", "description": "Create dynamic, rule-based gift card campaigns. Drive engagement with spend-based rewards, category offers & conditional vouchers."}, "tags": ["configurablePromotions", "categorySpecificCampaigns", "conditionalVouchers"], "borderColor": "#524C72", "backgroundColor": "#13072D", "index": 4, "tagsBg": "#0B0219", "color": "#C2B8FF"}, {"id": 4, "title": "valueAdded", "slug": "value-added-services", "meta": {"title": "Value Added Gift Card Services | YOUGotaGift Solutions", "description": "Boost your gift card program with white-label stores, mPOS portals, and seamless third-party integrations, easy to launch and manage."}, "tags": ["whiteLabelGiftCardMicrosite", "mpos", "corporateSalesPortal", "thirdPartyIntegrations"], "description": "valueAddedDesc", "borderColor": "#524C72", "backgroundColor": "#13072D", "index": 3, "tagsBg": "#0B0219", "color": "#C2B8FF"}, {"id": 5, "title": "jointBrand", "slug": "joinBrandsNetwork", "description": "jointBrandDesc", "tags": ["api technology", "api technology", "api technology", "api", "api technology", "api technology", "api technology"], "borderColor": "#524C72", "backgroundColor": "#13072D", "index": 2, "tagsBg": "#0B0219", "color": "#C2B8FF"}]}]}