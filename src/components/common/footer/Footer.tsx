"use client";
import React, { useEffect, useState } from "react";
import styles from "./Footer.module.scss";
import getConfig from "next/config";
import { supportWebP } from "utils/detectWebp";
import {
  FOOTER_ALIGNMENT,
  FOOTER_ITEM_TYPE,
  MENU_TYPE,
  PAGEURLS,
} from "@constants/common";
import { ECOM_BASE_URL, imageBaseUrl } from "@constants/envVariables";
import Link from "next/link";
import HiringTile from "../hiringTile/HiringTile";
import { Trans, useTranslation } from "next-i18next";
import footerData from "./footerData.json";
import { Box, Modal } from "@mui/material";
import ContactUs from "components/home/<USER>/ContactUs";

const Footer = () => {
  const { t, i18n } = useTranslation();

  const [webpSupported, setWebpSupported] = useState<boolean>(false);
  const [openModal, setOpenModal] = useState(false);

  useEffect(() => {
    supportWebP().then((result: any) => setWebpSupported(result));
  }, []);

  const handShake = `${imageBaseUrl}/images/handShake.svg`;
  const arrow = `${imageBaseUrl}/images/arrow-forward.svg`;
  const logo = `${imageBaseUrl}/images/cubeGray.svg`;

  const handleOpenModal = () => {
    setOpenModal(true);
  };

  const handleCloseModal = () => {
    setOpenModal(false);
  };

  const locale = i18n.language;
  // get the current year
  const currentYr = new Date().getFullYear();
  const copyrightNotice = `©${currentYr} YOUGotaGift.com Ltd.`;
  const subMenu: any = footerData?.subMenu;
  const footerStores = footerData?.footerStores;

  const SocialData: any = subMenu
    ?.filter(
      (item: any) => item?.itemCode === FOOTER_ITEM_TYPE.ITEM_CODE_OUR_SOCIAL
    )
    .flatMap((item: any) => item?.children?.edges);

  /**
   * @method getStoreLinkHref
   * @param storeLink
   * @returns string
   */
  const getStoreLinkHref = (storeLink: any) => {
    const storeCode = storeLink?.split("/")[2];
    return `/${storeCode}`;
  };

  return (
    <div className={`container ${styles["footer"]}`}>
      <div className={`${styles["footer__cta"]}`}>
        <div className={`${styles["footer__cta-left"]}`}>
          <div className={`${styles["footer__cta-container"]}`}>
            <span className={`${styles["footer__cta-label"]}`}>
              {t("inquire")}
            </span>{" "}
          </div>
        </div>
        <div className={`${styles["parent"]}`}>
          <div></div>
          <div
            className={`${styles["footer__cta-right"]}`}
            onClick={handleOpenModal}
          >
            <div className={`${styles["footer__label-container"]}`}>
              <div className={`${styles["footer__cta-label"]}`}>
                <img src={handShake} alt="handShake icone" />
                <span>{t("talkToExpert")}</span>{" "}
                <img
                  className={styles["footer__label-container--arrow"]}
                  src={arrow}
                  alt="forward arrow"
                />
              </div>
            </div>
          </div>
        </div>
      </div>

      <div className={styles["footer__content"]}>
        <div className={styles["footer__brand-logo"]}>
          <img src={logo} alt="" />
        </div>

        <div className={styles["footer__content-column"]}>
          <div className={styles["footer__tagline"]}>
            <h5>{t("solutionHub")}</h5>
            <p className="md-hide" data-testid="tagline">
              <Trans
              i18nKey={"footerTag"}
              />
            </p>

            <div className={styles["footer__social-icons"]}>
              {SocialData?.map(
                (
                  {
                    node: {
                      itemImage = "",
                      itemLabel = "",
                      itemUrl = "",
                      itemImageWebp = "",
                    } = {},
                  }: any,
                  index: number
                ) => (
                  <React.Fragment key={index}>
                    <Link href={itemUrl}>
                      <img
                        src={
                          webpSupported && itemImageWebp
                            ? `${imageBaseUrl}/images/${itemImageWebp}.svg` ||
                              itemImage
                            : itemImage
                        }
                        alt={itemLabel}
                      />
                    </Link>
                  </React.Fragment>
                )
              )}
            </div>
          </div>
          {subMenu?.map(
            (
              {
                itemLabel = "",
                itemLabelAr = "",
                children = { edges: [] },
                itemAlignment = "",
                itemCode = "",
              },
              index: number
            ) => (
              <React.Fragment key={index}>
                {itemAlignment === FOOTER_ALIGNMENT.VERTICAL &&
                  itemCode !== FOOTER_ITEM_TYPE.ITEM_CODE_SUPPORT && (
                    <div
                      className={styles["footer__menu"]}
                      key={index}
                      data-testid={`subFirstRowMenu`}
                    >
                      <h5>{locale == "ar" ? itemLabelAr : itemLabel}</h5>
                      <div className={styles["footer__item-list"]}>
                        {children?.edges?.map(
                          (
                            {
                              node: {
                                itemLabel = "",
                                itemUrl = "",
                                itemImage = "",
                                itemCode = "",
                                itemLabelAr = "",
                              } = {},
                            },
                            index: number
                          ) => (
                            <div key={index}>
                              {itemUrl &&
                                (itemImage === "" ? (
                                  <Link
                                    href={`${
                                      itemUrl?.indexOf("http") !== -1
                                        ? itemUrl
                                        : `${itemUrl}`
                                    }`}
                                    target={`${
                                      itemUrl?.indexOf("http") !== -1
                                        ? "_blank"
                                        : ``
                                    }`}
                                    data-testid="subMenuItem"
                                    className={
                                      itemCode == MENU_TYPE.CAREER
                                        ? styles["hiring-align"]
                                        : ""
                                    }
                                  >
                                    {locale == "ar" ? itemLabelAr : itemLabel}{" "}
                                    {itemCode == MENU_TYPE.CAREER ? (
                                      <HiringTile />
                                    ) : (
                                      ""
                                    )}
                                  </Link>
                                ) : (
                                  ""
                                ))}
                            </div>
                          )
                        )}
                      </div>
                    </div>
                  )}
              </React.Fragment>
            )
          )}
        </div>

        <div className={styles["footer__content-row"]}>
          <div className={styles["footer__copyright-container"]}>
            <div
              className={styles["footer__copyright"]}
              data-testid="copyright"
            >
              {copyrightNotice}
            </div>
            <Link href={`${ECOM_BASE_URL}${PAGEURLS.PRIVACY_POLICY}`} target="_blank">
              {t("privacyPolicy")}
            </Link>
            <Link href={`${ECOM_BASE_URL}${PAGEURLS.TERMS}`} target="_blank">
              {t("termsOfUse")}
            </Link>
          </div>

          <div className={styles["footer__available-regions"]}>
            {!!footerStores?.length ? t("availableIn") : ""}{" "}
            {footerStores?.map((item, index: number) => {
              return (
                <React.Fragment key={index}>
                  
                    <span key={index} data-testid="availableStores">
                      {locale == "ar" ? item?.store?.nameAr : item?.store?.name}
                      {footerStores?.length !== index + 1 && (
                        <>
                          <span className={styles.sep}>|</span>{" "}
                        </>
                      )}
                    </span>
                  
                </React.Fragment>
              );
            })}
          </div>
        </div>
      </div>
      {/* Contact Us Modal */}
      <Modal
        open={openModal}
        disableEscapeKeyDown
        disableAutoFocus
        disableEnforceFocus
        disableRestoreFocus
        disablePortal={false}
        hideBackdrop={false}
        disableScrollLock={false}
        onClose={handleCloseModal}
        aria-labelledby="contact-us-modal"
        aria-describedby="contact-us-form-modal"
        slotProps={{
          backdrop: {
            className: "modal__backdrop",
          },
        }}
      >
        <Box className="modal__content">
          <ContactUs onClose={handleCloseModal} />
        </Box>
      </Modal>
    </div>
  );
};

export default Footer;
