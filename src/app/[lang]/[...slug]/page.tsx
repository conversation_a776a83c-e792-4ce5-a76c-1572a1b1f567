import Header from "components/common/header/Header";
import styles from "./page.module.css";
import Banner from "components/product/banner/Banner";
import BackButton from "components/product/backButton/backButton";
import ForBlock from "components/product/forBlock/forBlock";
import CardInfo from "components/cardInfo/CardInfo";
import data from '../../../data.json';
import Cards, { Card } from "components/home/<USER>/cards/Cards";
import cardsData from "components/home/<USER>/cardsData.json";
import { CUSTOMER_TYPE } from "@constants/common";
import LandingMweb from "features/mweb/LandingMweb";

interface CardDataType {
  data: Card[];
  type: string;
};

const getCardInfoBySlug = (slug: string) : CardDataType => {
  const buyerCardInfo = cardsData.buyers[0]['cards'].find((card: any) => {
    return card.slug === slug
  });
  console.log('buyerCardInfo', buyerCardInfo);
  if(buyerCardInfo) {
    return {
      data : [buyerCardInfo],
      type: CUSTOMER_TYPE.BUYERS
    };
  } else {
    const brandCardInfo = cardsData.brands[0]['cards'].find((card: any) => card.slug === slug);
    if(brandCardInfo) {
      return {
        data : [brandCardInfo],
        type: CUSTOMER_TYPE.BRANDS
      };
    }
  }
  return {
    data : [],
    type: ''
  };
}

const getFeatureId = (data: any, featureSlug: string) => {
  const featureInfo = data?.features?.filter((item: any) => item?.slug == featureSlug);
  if(featureInfo && featureInfo.length > 0){
    return featureInfo[0]?.id;
  }
  return 1;
}

// You now have access to the current locale
// e.g. /en-US/products -> `lang` is "en-US"
export default async function ProductPage({
  params,
}: {
  params: any;
}) {

  const dataParams = await params;
  const productSlug =  dataParams?.slug[0];
  const featureSlug = dataParams?.slug[1];

  const selectedCard = getCardInfoBySlug(productSlug);
  const selectedCardInfo = data ? data.filter((item: any) => item?.slug == productSlug) : [];

  return (
<>

    <div className={styles.main}>
      <div className={styles.headerSection}>
        <Header />
        <Banner>
          {/* <BackButton /> */}
          <ForBlock customerType={selectedCard?.type} />
          <div className={styles['card-data-container']}>
            <div className={styles['card-container']}>
              <Cards cardsData={selectedCard?.type == CUSTOMER_TYPE.BRANDS ? cardsData.brands[0]['cards'] : cardsData.buyers[0]['cards']} type={selectedCard?.type} isDetailPage={true} />
            </div>
            <div className={styles['card-detail-container']}>
              {
                selectedCardInfo && <CardInfo data={selectedCardInfo[0]} tileId={getFeatureId(selectedCardInfo[0],featureSlug)} />
              }
            </div>
          </div>
        </Banner>
      </div>
    </div>
    <div className={styles['mweb-main']}>
    <LandingMweb/>
  </div>
</>

  );
}
