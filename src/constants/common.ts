export enum FOOTER_ALIGNMENT {
  VERTICAL = "VERTICAL",
  HORIZONTAL = "HORIZONTAL",
}

export enum FOOTER_ITEM_TYPE {
  ITEM_CODE_OUR_SOCIAL = "ITEM_CODE_OUR_SOCIAL",
  ITEM_CODE_OUR_SERVICES = "ITEM_CODE_OUR_SERVICES",
  ITEM_CODE_SUPPORT = "ITEM_CODE_SUPPORT",
  FB_ITEM_CODE = "FB_ITEM_CODE",
  ITEM_CODE_FOR_DEVELOPERS = "ITEM_CODE_FOR_DEVELOPERS",
}

export enum MENU_TYPE {
  CAREER = "CR_ITEM_CODE",
}

export enum PAGEURLS {
  HOME = "/",
  PRIVACY_POLICY = "/privacy-policy",
  TERMS = "/terms-of-use",
}

export enum PRODUCT{
  GIFT_CARD_API = 1,
  GIFT_SHOP = 2,
  AT_WORK = 3,
  GIFTING_WALLET = 4,
  CAMPAIGN_MANAGEMENT = 5,
  VALUE_ADDED_SERVICES = 6,
  CARD_PRODUCTS = 7
}

export enum FEATURE{
  TECHNOLOGY = 1,
  USE_CASE = 2,
  BRAND_NETWORK = 3,
  GUIDED_EXPERIENCE = 4,
  API_CLIENT = 5,
  BUSINESS = 1,
  BULK_ORDER = 2,
  WORK_PRO = 5,
  GIFT_SHOP_CLIENT = 4,
  LOYALTY_POINTS = 3,
  GIFT_CATALOG = 1,
  CONGIGURABLE_PRMOTION = 1,
  CATEGORY_SPECIFIC_CAMPAIGN = 2,
  CONDITIONAL_VOUCHERS = 3,
  WHITE_LABEL = 1,
  MPOS = 2,
  CORPORATE_SALES = 3,
  THIRD_PARTY = 4,
  WALLET = 6

}

export enum CUSTOMER_TYPE{
  BUYERS = 'buyers',
  BRANDS = 'brands',
}

export enum CARD_TITLE{
  JOIN_BRANDS = "jointBrand"
}
export enum API_ENDPOINTS {
  VERIFY_CAPTCHA = "/api/verify-recaptcha",
  SEND_INQUIRY = "/api/send-inquiry",
}

export enum TOAST_TYPE {
  CAPSULE = "capsule"
}
