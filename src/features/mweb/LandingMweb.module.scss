@import "@styles/variables";
@import "@styles/mixins";

.mweb-landing {
    background-color: #0B0219;
    color: white;

    .mweb-header {
        display: flex;
        margin-top: 16px;
        padding-inline-start: 20px;
        position: absolute;


        &__divider {
            width: 1px;
            background: #483C6C;
            margin-inline-start: 16px;
            margin-inline-end: 10px;
        }

        &__solution {
            display: flex;
            align-items: center;
            gap: 8px;
            font-size: 16px;
            font-weight: 600;
            color: #fff;

            span {
                color: #c2b8ff;
                font-weight: 600;
                font-size: 16px;
            }
        }
    }

    .mweb-banner-wrap {
        background: radial-gradient(70.71% 70.71% at 50% 50%,
                rgba(33, 10, 64, 0.06) 0%,
                rgba(33, 10, 64, 0) 100%),
            radial-gradient(70.71% 70.71% at 50% 50%,
                rgba(113, 61, 255, 0.3) 0%,
                rgba(113, 61, 255, 0) 100%);

        .banner {
            width: 100%;
            height: 500px;


            &__container {
                height: 100%;
            }
        }
    }
.mweb-card-wrap{
    margin-top: -220px;

    [dir="rtl"] & {
        margin-top: -190px;
    }
}
    .mweb-cards {
        margin-inline: 20px;
        padding: 16px;
        border-radius: 24px;
        border: 1px solid rgba(194, 184, 255, 0.30);
        background: #13072D;
        margin-bottom: 16px;

        &__title {
            border-radius: 16px;
            background: #B800C4;
            display: flex;
            align-items: center;
            gap: 6px;
            padding-block: 4px;
            padding-inline-end: 8px;
            width: max-content;

            &-dot {
                width: 12px;
                height: 12px;
                border-radius: 12px;
                background: #F985FF;
                margin-inline-start: 4px;
            }

            p {
                color: #FFF;
                font-family: "Mona Sans";
                font-size: 8px;
                font-weight: 500;
                line-height: 140%;
                letter-spacing: 0.32px;
                text-transform: uppercase;

                @include rtl-styles {
                    font-family: $arabic-font-family;
                  }
            }
        }
    }

    .mweb-msg {
        display: flex;
        align-items: center;
        margin-inline: 20px;
        gap: 16px;
        border-radius: 8px;
        background: rgba(194, 184, 255, 0.30);
        padding: 16px;
        margin-block: 23px;

        p {
            color: #FFF;
            font-family: "Mona Sans";
            font-size: 14px;
            font-style: normal;
            font-weight: 600;
            line-height: 16px;
            letter-spacing: -0.14px;

            @include rtl-styles {
                font-family: $arabic-font-family;
              }
        }
    }
}

.mweb-hero {
    display: flex;
    align-items: center;
    overflow: hidden;
    padding-block: 8px;
    padding-inline-start: 15px;
    background-color: #C2B8FF;

    &__tag {
        border-radius: 4px;
        background: #0B0219;
        padding: 4px 8px;
        white-space: nowrap;
        color: #C2B8FF;
        font-family: "Mona Sans";
        font-size: 10px;
        font-weight: 400;
        line-height: 16px;
        letter-spacing: -0.1px;

        @include rtl-styles {
            font-family: $arabic-font-family;
          }
    }

    &__list {
        flex: 1;
        overflow: hidden;
        position: relative;
        margin-inline: 8px;
    }

    &__marquee {
        display: inline-block;
        white-space: nowrap;
        animation: scroll-ltr 10s linear infinite;
        animation-delay: 0.5s;

        [dir="rtl"] & {
            animation: scroll-rtl 10s linear infinite;
            animation-delay: 0.5s;
        }

    }

    &__item {
        margin: 0 5px;
        color: #0B0219;
        font-family: "Mona Sans";
        font-size: 12px;
        font-weight: 500;
        line-height: 16px;
        letter-spacing: -0.12px;

        @include rtl-styles {
            font-family: $arabic-font-family;
          }
    }

    &__separator {
        margin: 0 2px;
        font-size: 12px;
        color: #B800C4;
    }
}

/* Animation for LTR marquee scrolling */
@keyframes scroll-ltr {
    0% {
        transform: translateX(0%);
    }

    100% {
        transform: translateX(-100%);
    }
}

/* Animation for RTL marquee scrolling */
@keyframes scroll-rtl {
    0% {
        transform: translateX(0%);
    }

    100% {
        transform: translateX(100%);
    }
}