{"giftCardAPI": "Gift Card API", "theTechnology": "The Technology", "useCasesFeatures": "Use Cases & Features", "ourBrandNetwork": "Our Brands Network", "guidedExp": "Guided Experience", "ourAPIClient": "Our API Clients", "apiTechDesc": "YOUGotaGift’s API is built on a modern microservice architecture powered by AWS and GCP, engineered for speed, security, and enterprise-grade reliability. It ensures seamless performance and effortless integrations, built to handle high demand and rapid growth with ease.", "useCasesDesc": "YOUGotaGift’s APIs are designed to support diverse business needs, from rewards and loyalty to large-scale digital gifting. With features like real-time delivery, multi-currency support, and webhooks, integration is flexible, fast, and scalable.", "ourBrandDesc": "Leverage from our extensive network of 1,000+ brands and growing across our catalog of popular and trending brands.", "guidedExpDesc": "YOUGotaGift provides intuitive onboarding dashboards that empower teams to collaborate and connect with our APIs. Get guided support through setup, integration, and testing, making it easy for developers and business teams to launch your Gift Card solution quickly.", "apiClientDesc": "", "dashboards": "Dashboards", "sandbox": "Sandbox", "integrationSpeed": "Integration Speed", "postmanCollection": "Postman Collection", "logNReport": "Log & Report", "swaggerUI": "Swagger UI", "6countries": "6 Countries", "19categories": "19 Categories", "5currencies": "5 Currencies", "210+uaebrands": "210+ UAE Brands", "150+ksabrands": "150+ KSA Brands", "110qatarbrands": "110+ Qatar Brands", "500japanbrands": "500+ Japan Brands", "300othergccbrands": "300+ Other GCC Brands", "800restbrands": "800+ Rest of the World Brands", "apiusecases": "API Use Cases", "apifeatures": "API Features", "loyaltyPrograms": "Loyalty Programs", "incentivePrograms": "Incentive Programs", "marketplace": "Marketplace", "rewards&Recognition": "Rewards & Recognition", "resellers": "Resellers", "aggregators": "Aggregators", "fintechs&SuperApps": "Fintechs & Super Apps", "payment&Wallets": "Payment & Wallets", "country": "Country", "currency": "<PERSON><PERSON><PERSON><PERSON>", "category": "Category", "brands": "Brands", "accountManagement": "Account Management", "orderManagement": "Order Management", "deliveryManagement": "Delivery Management", "apiPerformance": "API Performance & Uptime", "buyersLabel": "reselling & Distribution", "forBuyers": "For Buyers.", "buyersDescription": "Buy or resell Gift Cards for your business. Integrate with APIs & portals or buy directly from our marketplaces. We have many solutions to chose from", "brandsLabel": "Cards & Processing", "forBrands": "For Brands.", "brandsDescription": "Launch a Gift Card program for your brand. Issue cards and manage your program with Value Added Services. Or simply add your existing Gift Card to our distribution network", "cardTitle3": "Gift card API", "cardDesc3": "Integrate with Fast, Secure and Flexible APIs to deliver Gift Cards seamlessly", "technology": "Technology", "features": "Features", "brandNetwork": "Brands Network", "experience": "Experience", "giftShop": "Giftshop", "cardDesc4": "An plug and play online store for businesses to integrate and offer Gift Cards to their customers or business partners.", "storeFront": "The Storefront", "technologyEdge": "The Technology Edge", "loyaltyPoint": "Loyalty Points Plugin", "shopFeatures": "Shop Features", "workDesc": "An e-commerce platform for businesses to buy Gift Cards with ease and convenience.", "business": "Designed for Business", "bulkOrder": "Bulk Order", "customDesign": "Custom Designs", "assistedSales": "Assisted Sales", "workPro": "@Work Pro", "advancedFeature": "Advanced Feature", "atWork": "Work", "giftingWallet": "Gifting & Wallet", "giftingDesc": "An e-commerce platform for consumers to Buy and manage their Gift Cards.", "giftCatalog": "Gift Card Catalog", "individualGifting": "Individual Gifting", "groupGifting": "Group Gifting", "personalisation": "Personalization", "wallet": "Wallet", "cardProducts": "Card Products", "cardProdDesc": "Create and distribute digital or physical Gift Cards customized to your brand—ready for issuance, redemption, and tracking.", "giftCard": "Gift Cards", "singleBrands": "Single & Multi-Brand Cards", "closedLoops": "Closed Loop Cards", "new": "New", "newtWork": "A Network of 1,000+ Brands", "clients": "2,500+ Clients", "majorPrograms": "50+ Major Loyalty Programs", "talkToExpert": "Talk to an expert", "solutionHub": "Solutions Hub", "bannerSubTitle1": "From Card Issuance to Distribution", "bannerSubTitle2": "Total Gift Card Solutions for Buyers & Brands", "inquire": "Inquire now", "designedForBusiness": "Designed for Business", "businessDesc": "A business first platform enabling businesses to buy gift cards online with ease and convenience, offering a wide range of top brands.", "asistedSales": "Assisted Sales", "atWorkDesc": "Deliver the Gift Cards to multiple recipients by email, WhatsApp, SMS or download PDFs to bulk print in office.", "printAtHome": "Print at Home", "emailSms": "Email & SMS", "whatsApp": "WhatsApp", "customDesigns": "Custom Design", "customDesc": "Design our top selling multi-brands Gift Card, HappyYOU with your own branding and creatives.", "assistesDesc": "Engage sales assistant to help you step by step place an order for Gift Cards online.", "atWorkPro": "Work Pro", "proDesc": "@Work with advanced features including account management, insights, personalisation and more.", "acountManagement": "Account Management", "insights": "Insights", "personalise": "Personalization & more...", "availableIn": "Available in", "termsOfUse": "Terms of Use", "privacyPolicy": "Privacy Policy", "regionalBrandCoverage": "Regional Brand Coverage", "popularRegionalBrands": "Popular Regional Brands", "exploreMore": "Explore More", "99.99%": "99.99%", "<250ms": "<250ms", "5,000+": "5,000+", "1M+": "1M+", ">99.98%": ">99.98%", "uptimesla": "Uptime SLA", "avgapiresponse": "Avg API response *", "concurrentrequests": "Concurrent requests", "dailyapicallserved": "Daily API calls served", "requestsuccessrate": "Request Success Rate", "supportRealtime": "Support Real-time Notification", "webhook": "Webhook", "security": "Security & Compliance", "pciDSS": "PCI DSS", "compliant": "Compliant", "oauth": "OAuth 2.0", "jwtAuth": "Support", "endtoend": "End to End", "encryption": "Encryption", "ipwhitelisting": "IP Whitelisting", "enhancedSecurity": "Enhanced Security", "ourGiftShopClient": "Our Giftshop Clients", "giftShopUseCases": "Giftshop Use Cases", "giftShopFeatures": "Giftshop Features", "customerRewards": "Customer Re<PERSON>s", "marketplaces": "Marketplaces", "salesIncentives": "Sales Incentives", "whiteLabelled": "White Labelled", "responsiveDesign": "Responsive Design", "integratedGiftCardCatalog": "Integrated Gift Card Catalog", "multiPayment": "Multi Payment", "adminControls": "Admin Controls", "giftShopTechDesc": "Our Giftshop is a secure, webview - enabled platform designed for seamless integration within enterprise ecosystems, making it easy to embed into existing employee portals or mobile apps. With robust authentication, data protection, and configurable access controls, it delivers a flexible and compliant gifting experience tailored for corporate environments.", "loyaltyPoints": "Loyalty Points", "loyaltyPointsDesc": "Empower your customer to purchase Gift Cards with loyalty points. Through our RESTful APIs with secure OAuth 2.0 authentication, businesses can programmatically integrate the Giftshop into their platforms, enabling users to seamlessly convert loyalty points or rewards into digital Gift Cards.", "balancePoints": "Balance Points", "buyGiftPoints": "Buy and Gift with Points", "orderTracking": "Order Tracking", "programManagement": "Program Management", "programDesc": "Seamlessly manage the full lifecycle of your gift card program—from setup and branding to real-time monitoring, rule configurations, and fraud protection—through one centralized platform.", "campaignManagement": "Campaign Management", "campaignDesc": "Create, launch, and track targeted promotions using a dynamic rules engine—drive sales, reward behaviours, and optimise marketing ROI across channels", "valueAdded": "Value Added Services", "valueAddedDesc": "Extend the power of your Gift Card program with plug-and-play tools that simplify digital sales, in-store issuance, corporate distribution, and third-party expansion—without added complexity.", "jointBrand": "Join Brands Network", "jointBrandDesc": "Explore our curated collection of premium gift cards", "openLoop": "Open Loop Cards", "promoCards": "Promo Cards", "programSetup": "Program Setup & Branding", "centralizedControl": "Centralized Control & Configuration", "reportingSecurity": "Reporting & Security", "corporatePortals": "Corporate & Merchant Portals", "customerSuport": "Customer Support & Service Tools", "invoicePromo": "Invoice-Based Promotions", "categorySpecific": "Category Specific Campaigns", "productSpecific": "Product Specific Campaigns", "timeOffers": "Time-Bo<PERSON> Offers", "budgetControl": "Budget-Controlled Promotions", "conditionalVouchers": "Conditional Vouchers", "reactivationTarget": "Reactivation & Retargeting", "whiteLabel": "White-Label Gift Card Microsite", "merchantPoint": "Merchant Point of Sale", "corporateSales": "Corporate Sales Portal", "thirdParty": "Third-Party Integrations", "giftCatalogDesc": "Explore and choose from a wide range of gift cards – all in one place for every occasion and every brand you love!", "categoryWiseListing": "Category Wise Listing", "customDenominations": "Custom Denominations", "multiBrandSupport": "Multi Brand Support", "individualGiftingDesc": "Pick the perfect gift card for anyone, anytime — thoughtful gifting made easy and personal.", "easyGifting": "Easy Gifting", "digitalGifting": "Digital Gifts", "groupGiftingDesc": "Group together and organise a bigger and better gift!", "giftTogether": "Gift Together", "personalisedGifting": "Personalised Gifting", "flexibleContribution": "Flexible Contribution", "perosonalizationDesc": "Add Greeting to gifts. Send gift with video, photo, message, stickers & more…", "customGifts": "Custom Gifts", "personalisedGift": "Personalised Gifting", "walletDesc": "Collect, manage, and redeem all your gift cards in one smart wallet – organised, secure, and always within reach!", "giftingHub": "Digital Gifting Hub", "organisedGifting": "Organised Gifting", "trackGifts": "Track Your Gifts", "hiring": "Hiring", "searchCountry": "Search your country", "nameRequired": "Minimum characters allowed for Name is 2 and max allowed is 26", "invalidPhoneNumber": "Enter valid Phone number", "invalidEmailAddress": "Enter valid Email address", "howHelp": "How Can I Help?", "cardSpecilist": "Hi! I'm <PERSON>, your Gift Card specialist.", "assistYou": "I look forward to assisting you.", "lookingFor": "What are you looking for?", "placeHolder": "I am looking for a gift card program.", "sendInquiry": "Send Inquiry", "yourName": "Your Name", "yourBusiness": "Your Business Email", "mobileOptional": "Mobile (Optional)", "mwebMsg": "The Solution Hub is currently accessible only via desktop browsers. For an optimal experience, please access it from a desktop device.", "configurablePromotions": "Configurable Promotions", "categorySpecificCampaigns": "Category Specific Campaigns", "configPromoDesc": "Reward customers based on spend thresholds with dynamic value-back campaigns tied to transaction amounts.", "invoiceBasedPromotions": "Invoice-Based Promotions", "timeBoundOffers": "Time-Bo<PERSON> Offers", "budgetControlledPromotions": "Budget-Controlled Promotions", "categoryCampDesc": "Create targeted incentives linked to specific product categories or SKUs to influence purchase behavior and clear inventory.", "voucherDesc": "Issue reward vouchers based on pre-set conditions such as purchase behavior, cart contents, or user segments.", "whiteLabelGiftCardMicrosite": "White-Label Gift Card Microsite", "whiteLabelDesc": "A fully branded, customer-facing e-commerce site for selling digital gift cards online. Quick to launch and optimised for mobile and desktop.", "mpos": "MPOS", "mposDesc": "A standalone mPOS portal that enables merchants to issue and redeem gift cards instantly—no integration required.", "corporateSalesPortal": "Corporate Sales Portal", "corporateSalesDesc": "A secure portal designed for businesses to place and manage bulk gift card orders.", "thirdPartyIntegrations": "Third-Party Integrations", "thirdPartyDesc": "YGG provides managed integrations with your existing partners, aggregators, or distribution platforms.", "brandCustomisedInterface": "Brand-Customised interface", "giftingExperience": "Gifting Experience", "seamlessCheckoutExperience": "Seamless checkout experience", "supportsMultipleGiftCard": "Supports multiple gift card", "inStoreIssuanceAndRedemption": "In-store issuance & Redemption", "customerServiceCounters": "Customer service counters", "eventsOrKiosks": "Events or kiosks", "corporateWalletManagement": "Corporate wallet management", "tieredDiscountStructures": "Tiered discount structures", "orderTrackingAndReporting": "Order tracking and reporting", "accessControlledLogins": "Access-controlled logins", "restApi": "Rest API", "onboarding": "onboarding", "affiliateAndLoyaltyPartners": "affiliate and loyalty partners", "programSetUpDesc": "Launch and grow fully branded gift card programs—designed, deployed, and scaled to fit your business. ", "endToEndProgramSetup": "End-to-End Program Setup", "customBrandedSolutions": "Custom-Branded Solutions", "scalableAndModularArchitecture": "Scalable & Modular Architecture", "centralisedControlAndConfiguration": "Centralised Control & Configuration", "reportingAndSecurity": "Reporting & Security", "corporateAndMerchantPortals": "Corporate & Merchant Portals", "customerSupportAndServiceTools": "Customer Support & Service Tools", "centralDesc": "Manage every aspect of your program from a single portal—across channels, rules, and operations.", "centralisedProgramControl": "Centralised Program Control", "rulesAndConfigurationEngine": "Rules & Configuration Engine", "multiChannelEnablement": "Multi-Channel Enablement", "reportDesc": "Gain visibility and peace of mind with robust reporting tools and built-in fraud prevention.", "realTimeDashboards": "Real-Time Dashboards", "reportingAndReconciliation": "Reporting & Reconciliation", "fraudAndRiskManagement": "Fraud & Risk Management", "coporateDesc": "Enable partners and internal teams to manage B2B orders, wallets, and redemptions effortlessly.", "salesPortal": "Sales Portal", "b2bOrderManagement": "B2B Order Management", "bulkIssuanceTools": "Bulk Issuance Tools", "customerDesc": "Enhance the user experience with tools for smooth post-sale support and service resolution.", "customerSupportTools": "Customer Support Tools", "whiteLabelGiftCardEcommerce": "White Label Gift Card e-commerce", "merchantAndCorporatePortalAccess": "Merchant & Corporate Portal Access", "giftCards": "Gift Cards", "giftCardsDesc": "Launch branded digital and physical Gift Cards that can be issued, redeemed, and tracked across your retail or digital ecosystem.", "singleAndMultiBrandCards": "Single & Multi-Brand Cards", "closedLoopCards": "Closed Loop Cards", "openLoopCards": "Open Loop Cards", "singleOrMultiBrandSupport": "Single or Multi-Brand Support", "configurableDenominationAndValidity": "Configurable Denomination & Validity", "fullOmnichannelCompatibility": "Full Omnichannel Compatibility", "secureIssuanceWithRealTimeTracking": "Secure Issuance with Real-Time Tracking", "walletCardsDesc": "Enable closed-loop wallets for in-app or on-site spending, loyalty accumulation, or stored value redemption.", "instantTopUps": "Instant top-ups", "customerLinkedAccounts": "Customer-linked accounts", "seamlessIntegrationWithCheckoutSystems": "Seamless integration with checkout systems", "supportsMultiBrandUse": "Supports multi-brand use", "campaignPerformanceTracking": "Campaign performance tracking", "giftCardDesc": "Power your brand with customisable, closed-loop digital and physical gift cards. Designed for seamless issuance, redemption, and tracking across online and offline channels—perfect for retail, corporate, and promotional use cases.", "singleMultiDesc": "Offer flexible gifting solutions with single-brand cards focused on loyalty or multi-brand cards that give recipients more choice. Ideal for both standalone retailers and brand groups looking to create versatile, closed-loop gifting ecosystems.", "singleBrandGiftCards": "Single-Brand Gift Cards", "multiBrandGiftCards": "Multi-Brand Gift Cards", "retailGroupCards": "Retail Group Cards", "corporateGroupCards": "Corporate Group Cards", "closedDesc": "A cost-efficient, brand-controlled gift card solution redeemable only within your retail or service ecosystem. Faster to deploy and more flexible for in-store and online use—ideal for brands seeking a fully owned and customised experience.", "storeOnlyGiftCards": "Store-Only Gift Cards", "omnichannelGiftCards": "Omnichannel Gift Cards", "multiBrandSingleNetworkCards": "Multi-Brand, Single Network Cards", "corporateB2BGiftCards": "Corporate & B2B Gift Cards", "topUpReloadableCards": "Top-up & Reloadable Cards", "openDesc": "A versatile, payment-network-backed gift card that works across multiple merchants—both online and in-store. Ideal for incentive programs and gifting use cases where spend flexibility and broad acceptance are key.", "universalAcceptance": "Universal Acceptance", "restrictedOpenLoopCards": "Restricted Open Loop Cards", "corporateIncentiveCards": "Corporate Incentive Cards", "reloadableNonReloadable": "Reloadable & Non-Reloadable", "compliance": "Compliance", "promoDesc": "Single-use or limited-value gift cards designed for short-term campaigns, offers, or customer engagement. Promo Cards are perfect for driving acquisition, boosting spend, or rewarding specific actions—without long-term liabilities.", "acquisitionFirstPurchaseOffers": "Acquisition & First Purchase Offers", "cashbackSpendBackCampaigns": "Cashback & Spend-Back Campaigns", "refundStoreCreditCards": "Refund & Store Credit Cards", "timeBoundVouchers": "Time-Bound Vouchers", "conditionalUseCases": "Conditional Use Cases", "walletCardDesc": "A brand-owned, reloadable digital wallet that works exclusively within your retail or service ecosystem. Unlike open loop wallets, our solution keeps funds and spend within your network—ideal for refunds, promotions, and customer retention across digital and in-store channels.", "reloadableWalletForRepeatSpend": "Reloadable Wallet for Repeat Spend", "refundsStoreCredit": "Refunds & Store Credit", "multiChannelBrandControlledUsage": "Multi-Channel Brand-Controlled Usage", "customRulesRealTimeReporting": "Custom Rules & Real-Time Reporting", "idealForPromotionsLoyalty": "Ideal for Promotions & Loyalty", "back": "Back", "inquirySubmittedSuccessfully": "Your inquiry has been submitted successfully", "somethingWrong": "Something went wrong!", "inquiry": "Send Inquiry", "footerTag": "Total Gift Card Solutions <br/> for Buyers & Brands", "jobTitle": "Job Title", "companyName": "Company Name", "jobTitleRequired": "Enter Job Title", "companyNameRequired": "Enter Company Name"}